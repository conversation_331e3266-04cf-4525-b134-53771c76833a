#!/bin/bash

echo "Testing Vendor-Product Relationship API"
echo "======================================="

BASE_URL="http://localhost:8086"

echo "1. Testing health endpoint:"
curl -s "$BASE_URL/api/test/health" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/test/health"
echo -e "\n"

echo "2. Testing vendor count:"
curl -s "$BASE_URL/api/test/vendors/count" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/test/vendors/count"
echo -e "\n"

echo "3. Testing product count:"
curl -s "$BASE_URL/api/test/products/count" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/test/products/count"
echo -e "\n"

echo "4. Testing sample vendors:"
curl -s "$BASE_URL/api/test/vendors/sample" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/test/vendors/sample"
echo -e "\n"

echo "5. Testing products with vendor information (JOIN query):"
curl -s "$BASE_URL/api/test/products-with-vendors" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/test/products-with-vendors"
echo -e "\n"

echo "6. Testing products by vendor ID (vendor_id = 1):"
curl -s "$BASE_URL/api/test/products/vendor/1" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/test/products/vendor/1"
echo -e "\n"

echo "API Testing Complete!"
echo "====================="
echo "The database now contains:"
echo "- Vendors table with IT companies and their details"
echo "- Products table with foreign key relationship to vendors"
echo "- JOIN queries to get products with vendor information"
echo "- Proper normalization with vendor details separate from products"
