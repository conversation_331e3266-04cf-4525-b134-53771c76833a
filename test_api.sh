#!/bin/bash

echo "Testing Order Management API with IT Products and Services"
echo "========================================================="

# Test 1: Get all products (this will require authentication, so it might fail)
echo "1. Testing GET /api/products (might require authentication):"
curl -s -X GET http://localhost:8086/api/products | head -c 200
echo -e "\n"

# Test 2: Check if server is running
echo "2. Testing server health:"
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:8086/api/products

echo -e "\n3. Application should be running on http://localhost:8086"
echo "4. The database now contains IT-related products and services including:"
echo "   - Hardware: Dell OptiPlex Desktop, HP EliteBook Laptop, Cisco Switch"
echo "   - Services: 24/7 IT Helpdesk, Cloud Migration, DevOps Consulting"
echo "   - Software: Microsoft Office 365, Adobe Creative Cloud, Salesforce CRM"
echo "   - Security: Bitdefender Endpoint Security, Fortinet Firewall"
echo "   - And many more IT products and services!"

echo -e "\n5. To test with proper authentication, you'll need to:"
echo "   - Set up Keycloak server on http://localhost:8085"
echo "   - Get a JWT token from Keycloak"
echo "   - Use the token in Authorization header: 'Bearer <token>'"
