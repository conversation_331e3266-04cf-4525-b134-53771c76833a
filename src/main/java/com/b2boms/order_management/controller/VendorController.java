package com.b2boms.order_management.controller;

import com.b2boms.order_management.model.Vendor;
import com.b2boms.order_management.service.VendorService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/vendors")
@RequiredArgsConstructor
public class VendorController {
    private final VendorService vendorService;
    
    @GetMapping
    public ResponseEntity<List<Vendor>> getAllVendors() {
        return ResponseEntity.ok(vendorService.getAllVendors());
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<Vendor> getVendorById(@PathVariable Long id) {
        return vendorService.getVendorById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/name/{name}")
    public ResponseEntity<Vendor> getVendorByName(@PathVariable String name) {
        return vendorService.getVendorByName(name)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/email/{email}")
    public ResponseEntity<Vendor> getVendorByEmail(@PathVariable String email) {
        return vendorService.getVendorByEmail(email)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/location/{location}")
    public ResponseEntity<List<Vendor>> getVendorsByLocation(@PathVariable String location) {
        return ResponseEntity.ok(vendorService.getVendorsByLocation(location));
    }
    
    @GetMapping("/rating/{minRating}")
    public ResponseEntity<List<Vendor>> getVendorsByMinRating(@PathVariable Float minRating) {
        return ResponseEntity.ok(vendorService.getVendorsByMinRating(minRating));
    }
    

    
    @GetMapping("/with-products")
    public ResponseEntity<List<Vendor>> getAllVendorsWithProducts() {
        return ResponseEntity.ok(vendorService.getAllVendorsWithProducts());
    }
    
    @PostMapping
    public ResponseEntity<Vendor> createVendor(@RequestBody Vendor vendor) {
        return new ResponseEntity<>(vendorService.saveVendor(vendor), HttpStatus.CREATED);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<Vendor> updateVendor(@PathVariable Long id, @RequestBody Vendor vendor) {
        return vendorService.getVendorById(id)
                .map(existingVendor -> {
                    vendor.setVendor_id(id);
                    return ResponseEntity.ok(vendorService.saveVendor(vendor));
                })
                .orElse(ResponseEntity.notFound().build());
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteVendor(@PathVariable Long id) {
        return vendorService.getVendorById(id)
                .map(vendor -> {
                    vendorService.deleteVendor(id);
                    return new ResponseEntity<Void>(HttpStatus.NO_CONTENT);
                })
                .orElse(ResponseEntity.notFound().build());
    }
}
