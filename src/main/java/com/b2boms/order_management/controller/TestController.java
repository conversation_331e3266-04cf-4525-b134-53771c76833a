package com.b2boms.order_management.controller;

import com.b2boms.order_management.model.Product;
import com.b2boms.order_management.model.Vendor;
import com.b2boms.order_management.service.ProductService;
import com.b2boms.order_management.service.VendorService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
public class TestController {
    private final ProductService productService;
    private final VendorService vendorService;
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "Order Management API is running");
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/products/count")
    public ResponseEntity<Map<String, Object>> getProductCount() {
        List<Product> products = productService.getAllProducts();
        Map<String, Object> response = new HashMap<>();
        response.put("totalProducts", products.size());
        response.put("message", "Total products in database");
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/products/sample")
    public ResponseEntity<Map<String, Object>> getSampleProducts() {
        List<Product> products = productService.getAllProducts();
        Map<String, Object> response = new HashMap<>();
        response.put("totalProducts", products.size());
        
        // Get first 3 products as sample
        List<Product> sampleProducts = products.stream().limit(3).toList();
        response.put("sampleProducts", sampleProducts);
        response.put("message", "Sample of IT products and services loaded");
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/products/vendor/{vendorId}")
    public ResponseEntity<Map<String, Object>> getProductsByVendor(@PathVariable Long vendorId) {
        List<Product> products = productService.getProductsByVendorId(vendorId);
        Map<String, Object> response = new HashMap<>();
        response.put("vendorId", vendorId);
        response.put("productCount", products.size());
        response.put("products", products);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/vendors/count")
    public ResponseEntity<Map<String, Object>> getVendorCount() {
        List<Vendor> vendors = vendorService.getAllVendors();
        Map<String, Object> response = new HashMap<>();
        response.put("totalVendors", vendors.size());
        response.put("message", "Total vendors in database");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/vendors/sample")
    public ResponseEntity<Map<String, Object>> getSampleVendors() {
        List<Vendor> vendors = vendorService.getAllVendors();
        Map<String, Object> response = new HashMap<>();
        response.put("totalVendors", vendors.size());

        // Get first 3 vendors as sample
        List<Vendor> sampleVendors = vendors.stream().limit(3).toList();
        response.put("sampleVendors", sampleVendors);
        response.put("message", "Sample of IT vendors loaded");

        return ResponseEntity.ok(response);
    }

    @GetMapping("/products-with-vendors")
    public ResponseEntity<Map<String, Object>> getProductsWithVendors() {
        List<Product> products = productService.getAllProductsWithVendor();
        Map<String, Object> response = new HashMap<>();
        response.put("totalProducts", products.size());
        response.put("message", "Products with vendor information (JOIN query)");

        // Get first 3 products with vendor info as sample
        List<Product> sampleProducts = products.stream().limit(3).toList();
        response.put("sampleProducts", sampleProducts);

        return ResponseEntity.ok(response);
    }
}
