package com.b2boms.order_management.controller;

import com.b2boms.order_management.model.Product;
import com.b2boms.order_management.service.ProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
public class TestController {
    private final ProductService productService;
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "Order Management API is running");
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/products/count")
    public ResponseEntity<Map<String, Object>> getProductCount() {
        List<Product> products = productService.getAllProducts();
        Map<String, Object> response = new HashMap<>();
        response.put("totalProducts", products.size());
        response.put("message", "Total products in database");
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/products/sample")
    public ResponseEntity<Map<String, Object>> getSampleProducts() {
        List<Product> products = productService.getAllProducts();
        Map<String, Object> response = new HashMap<>();
        response.put("totalProducts", products.size());
        
        // Get first 3 products as sample
        List<Product> sampleProducts = products.stream().limit(3).toList();
        response.put("sampleProducts", sampleProducts);
        response.put("message", "Sample of IT products and services loaded");
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/products/vendor/{vendorId}")
    public ResponseEntity<Map<String, Object>> getProductsByVendor(@PathVariable Long vendorId) {
        List<Product> products = productService.getProductsByVendorId(vendorId);
        Map<String, Object> response = new HashMap<>();
        response.put("vendorId", vendorId);
        response.put("productCount", products.size());
        response.put("products", products);
        return ResponseEntity.ok(response);
    }
}
