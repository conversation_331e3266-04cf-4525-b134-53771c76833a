package com.b2boms.order_management.repository;

import com.b2boms.order_management.model.Vendor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VendorRepository extends JpaRepository<Vendor, Long> {
    
    // Find vendor by name
    Optional<Vendor> findByName(String name);
    
    // Find vendor by email
    Optional<Vendor> findByEmail(String email);
    
    // Find vendors by location
    List<Vendor> findByLocation(String location);
    
    // Find vendors with rating above a certain threshold
    @Query("SELECT v FROM Vendor v WHERE v.rating >= :minRating")
    List<Vendor> findByRatingGreaterThanEqual(@Param("minRating") Float minRating);
    
    // Get vendors with their product count
    @Query("SELECT v FROM Vendor v LEFT JOIN FETCH v.products")
    List<Vendor> findAllWithProducts();
}
