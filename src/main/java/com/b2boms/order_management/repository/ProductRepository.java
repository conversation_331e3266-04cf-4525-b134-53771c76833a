package com.b2boms.order_management.repository;

import com.b2boms.order_management.model.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    @Query("SELECT p FROM Product p WHERE p.vendor_id = :vendorId")
    List<Product> findByVendorId(@Param("vendorId") Long vendorId);
}