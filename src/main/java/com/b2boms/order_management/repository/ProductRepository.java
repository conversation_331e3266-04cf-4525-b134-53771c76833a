package com.b2boms.order_management.repository;

import com.b2boms.order_management.model.Product;
import com.b2boms.order_management.model.Vendor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    // Find products by vendor ID
    @Query("SELECT p FROM Product p WHERE p.vendor.vendor_id = :vendorId")
    List<Product> findByVendorId(@Param("vendorId") Long vendorId);

    // Find products by vendor object
    List<Product> findByVendor(Vendor vendor);

    // Find products with vendor information (JOIN FETCH to avoid N+1 problem)
    @Query("SELECT p FROM Product p JOIN FETCH p.vendor")
    List<Product> findAllWithVendor();

    // Find products by vendor name
    @Query("SELECT p FROM Product p JOIN p.vendor v WHERE v.name = :vendorName")
    List<Product> findByVendorName(@Param("vendorName") String vendorName);

    // Find products by vendor location
    @Query("SELECT p FROM Product p JOIN p.vendor v WHERE v.location = :location")
    List<Product> findByVendorLocation(@Param("location") String location);

    // Find products by vendor rating above threshold
    @Query("SELECT p FROM Product p JOIN p.vendor v WHERE v.rating >= :minRating")
    List<Product> findByVendorRatingGreaterThanEqual(@Param("minRating") Float minRating);
}