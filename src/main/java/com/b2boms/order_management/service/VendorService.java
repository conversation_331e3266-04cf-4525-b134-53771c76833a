package com.b2boms.order_management.service;

import com.b2boms.order_management.model.Vendor;
import com.b2boms.order_management.repository.VendorRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class VendorService {
    private final VendorRepository vendorRepository;
    
    public List<Vendor> getAllVendors() {
        return vendorRepository.findAll();
    }
    
    public Optional<Vendor> getVendorById(Long id) {
        return vendorRepository.findById(id);
    }
    
    public Optional<Vendor> getVendorByName(String name) {
        return vendorRepository.findByName(name);
    }
    
    public Optional<Vendor> getVendorByEmail(String email) {
        return vendorRepository.findByEmail(email);
    }
    
    public List<Vendor> getVendorsByLocation(String location) {
        return vendorRepository.findByLocation(location);
    }
    
    public List<Vendor> getVendorsByMinRating(Float minRating) {
        return vendorRepository.findByRatingGreaterThanEqual(minRating);
    }
    

    
    public List<Vendor> getAllVendorsWithProducts() {
        return vendorRepository.findAllWithProducts();
    }
    
    public Vendor saveVendor(Vendor vendor) {
        return vendorRepository.save(vendor);
    }
    
    public void deleteVendor(Long id) {
        vendorRepository.deleteById(id);
    }
}
