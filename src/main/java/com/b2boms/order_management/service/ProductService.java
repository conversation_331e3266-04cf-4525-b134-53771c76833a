package com.b2boms.order_management.service;

import com.b2boms.order_management.model.Product;
import com.b2boms.order_management.model.Vendor;
import com.b2boms.order_management.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ProductService {
    private final ProductRepository productRepository;

    public List<Product> getAllProducts() {
        return productRepository.findAll();
    }

    public List<Product> getAllProductsWithVendor() {
        return productRepository.findAllWithVendor();
    }

    public Optional<Product> getProductById(Long id) {
        return productRepository.findById(id);
    }

    public List<Product> getProductsByVendorId(Long vendorId) {
        return productRepository.findByVendorId(vendorId);
    }

    public List<Product> getProductsByVendor(Vendor vendor) {
        return productRepository.findByVendor(vendor);
    }

    public List<Product> getProductsByVendorName(String vendorName) {
        return productRepository.findByVendorName(vendorName);
    }

    public List<Product> getProductsByVendorLocation(String location) {
        return productRepository.findByVendorLocation(location);
    }

    public List<Product> getProductsByVendorRating(Float minRating) {
        return productRepository.findByVendorRatingGreaterThanEqual(minRating);
    }

    public Product saveProduct(Product product) {
        return productRepository.save(product);
    }

    public void deleteProduct(Long id) {
        productRepository.deleteById(id);
    }
}