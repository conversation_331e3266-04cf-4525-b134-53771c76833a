spring.application.name=order_management

# Database Configuration
spring.datasource.url=****************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Keycloak Configuration
        #   issuer-uri: ${KEYCLOAK_AUTH_URL:http://localhost:8085}/realms/${KEYCLOAK_REALM:b2b-realm}
        #   jwk-set-uri: ${KEYCLOAK_AUTH_URL:http://localhost:8085}/realms/${KEYCLOAK_REALM:b2b-realm}/protocol/openid-connect/certs

spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8085/realms/b2b-realm
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8085/realms/b2b-realm/protocol/openid-connect/certs

# Logging
logging.level.org.springframework.security=DEBUG

server.port=8086